<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Famille extends Model
{
    public $timestamps = false;

    protected $table = 'familles';
    protected $fillable = [
        'nom',
        'adresse',
        'observation'
    ];

    public function mambras()
    {
        return $this->belongsToMany(Mambra::class, 'famille_mambra', 'famille_id', 'mambra_person_code', 'id', 'person_code');
    }
}
