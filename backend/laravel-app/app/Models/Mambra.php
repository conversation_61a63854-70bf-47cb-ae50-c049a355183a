<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Mambra extends Model
{
    protected $table = 'mambras';
    
    protected $fillable = [
        'nom',
        'prenom',
        'date_naissance',
        'sexe',
        'date_bapteme',
        'telephone',
        'situation_matrimoniale',
        'occupation',
        'observation',
        'person_code',
    ];

    public function familles(){
        return $this->belongsToMany(Famille::class, 'famille_mambra', 'mambra_person_code', 'famille_id', 'person_code', 'id');
    }
}
