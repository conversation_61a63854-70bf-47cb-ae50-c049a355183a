<?php

namespace App\Http\Controllers;

use App\Models\Mambra;
use App\Models\Famille;
use Illuminate\Http\Request;
use App\Http\Requests\FamilleRequest;
use App\Http\Resources\FamilleResource;
use Illuminate\Support\Facades\DB;

class FamilleController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $familles = Famille::all();
        return FamilleResource::collection($familles);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(FamilleRequest $request)
    {
       $famille  = DB::transaction(function () use ($request) {
            $famille = new Famille ([
            'nom' => $request->get('nom'),
            'adresse' => $request->get('adresse'),
            'observation' => $request->get('observation'),
        ]);
            $famille->saveOrFail();
            return $famille;
        });

        return FamilleResource::make($famille);
    }


    /**
     * Display the specified resource.
     */
    public function show(Famille $famille)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Famille $famille)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Famille $famille)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Famille $famille)
    {
        //
    }

    /**
     * Add members to the specified famille using person_code.
     */
    public function addMembers(Request $request, Famille $famille)
    {
        $personCodes = $request->all()['personCodes'];

        foreach ($personCodes as $personCode) {
            $famille->addMembreByCode($personCode);
        }

        $familles = Famille::all();
        return FamilleResource::collection($familles); 
    }

}
