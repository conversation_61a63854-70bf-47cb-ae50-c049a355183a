<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use App\Models\Mambra;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Excel;
use App\Imports\MambrasImport;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use App\Http\Requests\MambraRequest;
use App\Http\Resources\MambraResource;
use Maatwebsite\Excel\Facades\Excel as FacadesExcel;

class MambraController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $mambra = Mambra::all();
        return MambraResource::collection($mambra);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(MambraRequest $request)
    {
        $c = DB::transaction(function () use ($request) {
            $mambra = new Mambra ([
            'nom' => $request->get('nom'),
            'prenom' => $request->get('prenom'),
            'date_naissance' => Carbon::parse($request->get('dateNaissance'))->format('Y-m-d'),
            'sexe' => $request->get('sexe'),
            'date_bapteme' => Carbon::parse($request->get('dateBapteme'))->format('Y-m-d'),
            'telephone' => $request->get('telephone'),
            'situation_matrimoniale' => $request->get('situationMatrimoniale'),
            'occupation' => $request->get('occupation'), 
            'observation' => $request->get('observation'),
        ]);
            $mambra->saveOrFail();
            return $mambra;
        });

        return MambraResource::make($c);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(MambraRequest $request, Mambra $mambra)
    {

        $mambra->update($request->all());
        return MambraResource::make($mambra);

    }


    /**
     * Display the specified resource.
     */
    public function show(Mambra $mambra)
    {
        return MambraResource::make($mambra);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Mambra $mambra)
    {
        $mambra->delete();
        $mambras = Mambra::all();
        return MambraResource::collection($mambras);

    }

    public function import(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:xlsx,xls,csv'
        ]);

        try {
            FacadesExcel::import(new MambrasImport, $request->file('file'));
            
            return response()->json(['message' => 'Importation réussie']);
            
        } catch (\Maatwebsite\Excel\Validators\ValidationException $e) {
            $failures = $e->failures();
            $errors = [];
            
            foreach ($failures as $failure) {
                $errors[] = [
                    'row' => $failure->row(),
                    'attribute' => $failure->attribute(),
                    'errors' => $failure->errors(),
                    'values' => $failure->values()
                ];
            }
            
            return response()->json(['error' => 'Erreurs de validation', 'details' => $errors], 422);
            
        } catch (\Exception $e) {
            return response()->json(['error' => 'Erreur lors de l\'importation: ' . $e->getMessage()], 500);
        }
    }
    
    // Method to get a mambra by person_code
    public function getByPersonCode($personCode)
    {
        $mambra = Mambra::where('person_code', $personCode)->first();
        if ($mambra) {
            return MambraResource::make($mambra);
        }
        return response()->json(['error' => 'Mambra not found'], 404);
    }
}

